import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { updateDocumentTitle } from '../utils/titleUpdater';
import DigestModal from './DigestModal';
import { API_CONFIG, getApiUrl } from '../config';
import { handleApiResponse } from '../utils/apiUtils';
import './LanguageSwitcher.css';

const LanguageSwitcher = () => {
  const { i18n, t } = useTranslation();
  const currentLanguage = i18n.language;
  const [isDigestModalOpen, setIsDigestModalOpen] = useState(false);
  const [digestData, setDigestData] = useState(null);
  const [isDigestLoading, setIsDigestLoading] = useState(false);
  const [digestError, setDigestError] = useState(null);

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
    updateDocumentTitle(lng);
  };

  const handleDigestClick = async () => {
    setIsDigestModalOpen(true);
    setIsDigestLoading(true);
    setDigestError(null);
    setDigestData(null);

    try {
      const response = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.DIGEST));
      const data = await handleApiResponse(response);
      setDigestData(data);
    } catch (error) {
      console.error('Error fetching digest:', error);
      setDigestError(error.message);
    } finally {
      setIsDigestLoading(false);
    }
  };

  const closeDigestModal = () => {
    setIsDigestModalOpen(false);
    setDigestData(null);
    setDigestError(null);
  };

  // Update title on component mount
  React.useEffect(() => {
    updateDocumentTitle(currentLanguage);
  }, [currentLanguage]);

  return (
    <>
      <div className="language-switcher">
        <button
          className={`digest-button ${isDigestLoading ? 'loading' : ''}`}
          onClick={handleDigestClick}
          disabled={isDigestLoading}
        >
          {isDigestLoading ? (
            <>
              <span className="digest-spinner"></span>
              {t('digest.loading')}
            </>
          ) : (
            <>
              📊 {t('digest.title')}
            </>
          )}
        </button>
        <button
          className={`language-button ${currentLanguage === 'en' ? 'active' : ''}`}
          onClick={() => changeLanguage('en')}
        >
          EN
        </button>
        <button
          className={`language-button ${currentLanguage === 'uk' ? 'active' : ''}`}
          onClick={() => changeLanguage('uk')}
        >
          UK
        </button>
      </div>

      <DigestModal
        isOpen={isDigestModalOpen}
        onClose={closeDigestModal}
        digestData={digestData}
        isLoading={isDigestLoading}
        error={digestError}
      />
    </>
  );
};

export default LanguageSwitcher;
