.language-switcher {
  display: flex;
  gap: 8px;
  margin-left: auto;
  align-items: center;
}

.language-button {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.language-button:hover {
  background-color: #e0e0e0;
}

.language-button.active {
  background-color: #673AB7;
  color: white;
  border-color: #673AB7;
}

/* Digest Button Styles */
.digest-button {
  background: linear-gradient(135deg, #673AB7, #9C27B0);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 8px rgba(103, 58, 183, 0.3);
  white-space: nowrap;
}

.digest-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(103, 58, 183, 0.4);
}

.digest-button:disabled {
  cursor: not-allowed;
  opacity: 0.8;
}

.digest-button.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

.digest-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .digest-button {
    padding: 6px 12px;
    font-size: 12px;
  }

  .language-switcher {
    gap: 6px;
  }
}
