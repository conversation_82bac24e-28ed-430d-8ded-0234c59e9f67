import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import LanguageSwitcher from './LanguageSwitcher';
import { updateDocumentTitle } from '../utils/titleUpdater';

// Mock the titleUpdater
vi.mock('../utils/titleUpdater', () => ({
  updateDocumentTitle: vi.fn()
}));

// Mock the DigestModal component
vi.mock('./DigestModal', () => ({
  default: ({ isOpen, onClose }) =>
    isOpen ? <div data-testid="digest-modal" onClick={onClose}>Digest Modal</div> : null
}));

// Mock the config and API utils
vi.mock('../config', () => ({
  API_CONFIG: {
    ENDPOINTS: {
      DIGEST: '/digest'
    }
  },
  getApiUrl: (endpoint) => `http://localhost:5000/api${endpoint}`
}));

vi.mock('../utils/apiUtils', () => ({
  handleApiResponse: vi.fn()
}));

// Mock fetch
global.fetch = vi.fn();

describe('LanguageSwitcher Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders language buttons and digest button correctly', () => {
    render(<LanguageSwitcher />);

    expect(screen.getByText('EN')).toBeInTheDocument();
    expect(screen.getByText('UK')).toBeInTheDocument();
    expect(screen.getByText(/Latest Digest/)).toBeInTheDocument();
  });

  it('marks the current language button as active', () => {
    render(<LanguageSwitcher />);

    // Default language is 'uk' as mocked in setup.js
    const ukButton = screen.getByText('UK');
    const enButton = screen.getByText('EN');

    expect(ukButton.className).toContain('active');
    expect(enButton.className).not.toContain('active');
  });

  it('calls changeLanguage and updateDocumentTitle when language button is clicked', () => {
    render(<LanguageSwitcher />);

    const enButton = screen.getByText('EN');
    fireEvent.click(enButton);

    // Should call updateDocumentTitle with 'en'
    expect(updateDocumentTitle).toHaveBeenCalledWith('en');
  });

  it('updates document title on component mount', () => {
    render(<LanguageSwitcher />);

    // Should call updateDocumentTitle with current language ('uk')
    expect(updateDocumentTitle).toHaveBeenCalledWith('uk');
  });

  it('opens digest modal when digest button is clicked', async () => {
    const mockResponse = { metadata: { generated_on: '2025-05-26' } };
    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    });

    const { handleApiResponse } = await import('../utils/apiUtils');
    handleApiResponse.mockResolvedValueOnce(mockResponse);

    render(<LanguageSwitcher />);

    const digestButton = screen.getByText(/Latest Digest/);
    fireEvent.click(digestButton);

    await waitFor(() => {
      expect(screen.getByTestId('digest-modal')).toBeInTheDocument();
    });
  });

  it('shows loading state when digest is being fetched', async () => {
    // Mock a delayed response
    global.fetch.mockImplementationOnce(() =>
      new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: () => Promise.resolve({})
      }), 100))
    );

    render(<LanguageSwitcher />);

    const digestButton = screen.getByText(/Latest Digest/);
    fireEvent.click(digestButton);

    // Should show loading text
    expect(screen.getByText(/Generating digest.../)).toBeInTheDocument();
  });
});
