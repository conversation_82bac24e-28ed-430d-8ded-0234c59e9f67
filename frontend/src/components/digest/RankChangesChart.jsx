import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const RankChangesChart = ({ data }) => {
  const { t } = useTranslation();

  if (!data || data.length === 0) {
    return (
      <div className="chart-section">
        <h3>{t('digest.charts.rankChanges.title')}</h3>
        <div className="no-data-message">
          {t('digest.charts.rankChanges.noChanges')}
        </div>
      </div>
    );
  }

  const chartData = {
    labels: data.map(player => player.nickname),
    datasets: [
      {
        label: t('digest.charts.rankChanges.gamesPlayed'),
        data: data.map(player => player.total_games_played),
        backgroundColor: 'rgba(54, 162, 235, 0.8)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
        yAxisID: 'y',
      },
      {
        label: t('digest.charts.rankChanges.winRate'),
        data: data.map(player => player.win_rate_percentage),
        backgroundColor: 'rgba(255, 99, 132, 0.8)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
        yAxisID: 'y1',
      }
    ],
  };

  const options = {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: t('digest.charts.rankChanges.title'),
        font: {
          size: 16,
          weight: 'bold'
        }
      },
      tooltip: {
        callbacks: {
          afterLabel: function(context) {
            const dataIndex = context.dataIndex;
            const player = data[dataIndex];
            return [
              `${t('digest.charts.rankChanges.status')}: ${t(`digest.charts.rankChanges.${player.status.toLowerCase()}`)}`,
              `${player.wins}W / ${player.losses}L`
            ];
          }
        }
      }
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Players'
        }
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: t('digest.charts.rankChanges.gamesPlayed')
        },
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: t('digest.charts.rankChanges.winRate') + ' (%)'
        },
        grid: {
          drawOnChartArea: false,
        },
        max: 100,
        min: 0,
      },
    },
  };

  return (
    <div className="chart-section">
      <div className="rank-changes-info">
        {data.map((player, index) => (
          <div key={index} className={`rank-change-item ${player.status.toLowerCase()}`}>
            <span className="player-name">{player.nickname}</span>
            <span className="status-badge">
              {t(`digest.charts.rankChanges.${player.status.toLowerCase()}`)}
            </span>
            <span className="win-rate">{player.win_rate_percentage.toFixed(1)}%</span>
          </div>
        ))}
      </div>
      <div className="chart-container">
        <Bar data={chartData} options={options} />
      </div>
    </div>
  );
};

export default RankChangesChart;
