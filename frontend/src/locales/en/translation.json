{"app": {"title": "Team Balancer App", "players": "Players", "balancedTeams": "Balanced Teams"}, "players": {"nickname": "Nickname", "score": "Score", "actions": "Actions", "winLoss": "W/L", "addFirstPlayer": "Add your first player here", "addPlayer": "Add", "add": "Add", "remove": "Remove", "removeAll": "Remove All", "showAllKnown": "Show all known players", "allKnownFromSheet": "All Known Players from Sheet", "noPlayersYet": "No players added yet. Use the form below to add players.", "alreadyInList": "Player {{nickname}} is already in the list.", "countComments": {"waiting": "⏳ Waiting...", "needMore": "📢 WE NEED MORE PEOPLE", "almostHere": "🔥 Almost here!", "letsGo": "🚀 LETS GOOOO", "strange": "😅 Thats strange, but okay", "findOneMore": "🤔 Find one more to be even"}}, "teams": {"team1": "Team 1", "team2": "Team 2", "noPlayers": "No players yet", "copyTeams": "Copy Teams", "format": "Format:", "copyToClipboard": "Copy to Clipboard", "copied": "Copied!", "teamsAutoCopied": "Teams automatically copied to clipboard!", "balancePerfect": "Perfect balance", "balanceGood": "Good balance", "balanceFair": "Fair balance", "balancePoor": "Poor balance", "balancePercentage": "{{percentage}}% balanced", "pointDifference": " ({{difference}} point difference)", "total": "Total", "higher": "HIGHER", "lower": "LOWER", "equal": "EQUAL", "leader": "Leader", "winner": "WINNER", "submitting": "Submitting game result...", "submissionSuccess": "Game result submitted successfully!", "submissionError": "Error submitting game result", "notEnoughPlayers": "Both teams need players to submit a game result", "clickToSelect": "Click to select winner", "secretRequired": "Admin secret required to submit game result"}, "balance": {"balanceTeams": "Balance Teams", "balancing": "Balancing...", "randomness": "Randomness: {{value}}%", "statusTitle": "Balancing Status", "statusDescription": "Player scores determine how teams are balanced. Here's what each score means:", "loading": "Loading", "refresh": "Refresh", "description": "Description", "noPlayers": "Please add players first!", "error": "Failed to balance teams. Please try again later.", "needAtLeastTwoPlayers": "Need at least 2 players to balance teams", "tooManyPlayers": "Too many players (maximum 30)", "needEvenNumberOfPlayers": "Need an even number of players to balance teams", "score4": "Positive balance", "score3": "No need to be balanced", "score2": "Moderate balance", "score1": "Needs to be significantly balanced", "score0": "Unbalanceable", "scoreMinus1": "Ban"}, "admin": {"secretTitle": "Admin Secret Required", "secretLabel": "Enter Admin Secret", "secretPlaceholder": "Format: admin:password", "submit": "Submit", "cancel": "Cancel", "contactForSecret": "Contact me to get a secret if you don't have one", "secretMissing": "Admin secret is required to submit game results", "invalidFormat": "Invalid format. Must be 'admin:password'", "emptyName": "Admin name cannot be empty", "emptyPassword": "Admin password cannot be empty", "logout": "Log Out"}, "developer": {"contacts": "Developer Contacts:", "discord": "Discord: @mykytahordia", "github": "Github: MykytaHordia", "cossacks3": "Cossacks 3: \"my<PERSON>ta.\"", "version": "Version: {{version}}"}, "digest": {"title": "Latest Digest", "loading": "Generating digest...", "error": "Failed to load digest data", "close": "Close", "downloadGames": "Download All Games Data", "downloadInfo": "All provided infographics are based on games played between the specified dates. You can review these games in detail by downloading the complete dataset.", "generatedOn": "Generated on {{date}}", "period": "Period: {{start}} - {{end}}", "sections": {"rankChanges": "Changes in Ranks", "topPlayers": "Top Active Players", "topAdmins": "Top Admins by Contribution", "monthlyActivity": "Monthly Activity", "weeklyActivity": "Weekly Activity", "dailyActivity": "Daily Activity"}, "charts": {"rankChanges": {"title": "Player Status Changes", "promote": "Promote", "demote": "Demote", "noChanges": "No rank changes this period", "gamesPlayed": "Games Played", "wonGames": "Won Games", "winRate": "Win Rate", "currentScore": "Current Score", "newScore": "New Score", "status": "Status"}, "topPlayers": {"title": "Most Active Players", "gamesCount": "Games Played", "noData": "No player data available"}, "topAdmins": {"title": "Admin Contributions", "gamesManaged": "Games Managed", "noData": "No admin data available"}, "activity": {"hourly": "Games by Hour of Day", "weekly": "Games by Day of Week", "daily": "Daily Activity", "hour": "Hour", "day": "Day", "month": "Month", "dayOfMonth": "Day of Month", "games": "Games", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}}}}